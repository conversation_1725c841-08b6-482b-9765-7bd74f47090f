-![alt text](image.png)

-เริ่ม implement ฟังก์ชั้นค้นหา ยังไม่ต้อง cache ข้อมูล ยึดหลักเดิม คือให้แสดงผลการค้นหา เฉพาะคะแนนจาก sheet ที่อาจารย์คนนั้นเป็นผู้อัปโหลดข้อมูลเท่านั้น โดยรายวิชานั้นต้องอยู่ในสภาวะ active โดยเช็คที่ sheet course_list ก่อนว่า อาจารย์คนนั้นอัปโหลดไฟล์ชื่ออะไรบ้าง และ อยู่ในสภาวะ active หรือไม่
-การแสดงการ์ด ของผลการค้นหาใหเ้แยกของ นศ แต่ละคน ตรวจสอบการดึง stat จาก col ที่สร้างไว้แล้วมาแสดงผลให้ถูกต้อง แสดงเป็นการ์ดที่ expand เลยครับ

***
- เริ่ม implement ฟังก์ชั้นค้นหา ผมสนใจการทำงานแบบ batch and cache ข้อมูลไว้ background เมื่อโหลด webapp ยึดหลักเดิม คือให้แสดงเฉพาะคะแนนจาก sheet ที่อาจารย์คนนั้นเป็นผู้อัปโหลดข้อมูลเท่านั้น ถ้าจะให้ไปเช็คที่ sheet course_list ก่อนว่า อาจารย์คนนั้นอัปโหลดไฟล์ชื่ออะไรบ้าง แล้วจึง cache sheet ที่อาจารย์คนนั้นเป็นผู้อัปโหลดข้อมูล  แนวทางนี้เหมาะสมหรือไม่ เน้นให้ทำงานเร็ว

- tab อัปโหลดคะแนน section 2 วงกลมที่มีเลขสอง เมื่อแสดงบน mobile มีการหด ให้แก้ไขให้เหมือน วงกลมที่มีเลขหนึ่ง และวงกลมที่มีเลขสาม

***
ในการเปิด webapp ครั้งแรก instructor mode เมื่อกด tab จัดการคะแนน จะมี spinner อัน ให้เอาอันสีฟ้าออก
***
จากการโหลดหน้า webapp ของ instructor mode เนื่องจากหน้าแรกที่จะแสดง เป็น tab ค้นหาคะแนน ดังนั้น จึงยังไม่มีการเรียกดูข่้อมูลคะแนนใดๆ ทำไมจึงมีบางครั้งแสดงว่าไม่พบข้อมูล ตรวจสอบ logic การโหลด webapp ของ instructor mode และแก้ไขครับ

***
- ส่วน อัปโหลดคะแนน ยังมีปัญหาครับ เมื่อกดอัปโหลด ไม่สามารถอัปได้
 เมื่อกรอกรายละเอียดวิชา และเลือกไฟล์ csv สามารถทำได้ปกติ เมื่อกดอัพโหลดคะแนน ก็แสดงตัวอย่างได้ถูกต้อง แต่เมื่อกดอัพโหลดคะแนน ตัวอย่างหายไป และ UI แสดง error
เกิดข้อผิดพลาด
เกิดข้อผิดพลาด: Exception: The number of columns in the data does not match the number of columns in the range. The data has 6 but the range has 5.

- เมื่อไปดูใน sheet พบการสร้าง sheet รายวิชาใหม่ แต่ไม่มีคะแนน มีแต่ header และไม่มีการสร้าง row รายวิชาใหม่นี้ใน sheet course_list ครับ

แก้ไข แต่ไม่ต้องทดสอบครับ เป็น GAS ผมต้อง push แล้วทดสอบเองครับ
***
-เกิด HTTP error: 408 Request Timeout 
Request ID: 0180372e-168c-4e8f-a1c6-1057d1e065c6 ใน augment code 
คุณปรับโค้ดเสร็จหรือยังครับ

-ผมลองทดสอบพบว่าตอนนี้ เมื่อกด download template จะเกิด spinner กำลังสร้าง template... ผมไม่คิดว่าจำเป็นครับ ตรวจสอบและปรับแก้ให้เหมาะสม

- เมื่อกรอกรายละเอียดวิชา และเลือกไฟล์ csv สามารถทำได้ปกติ เมื่อกดอัพโหลดคะแนน ก็แสดงตัวอย่างได้ถูกต้อง แต่เมื่อกดอัพโหลดคะแนน ตัวอย่างหายไป และ UI แสดง error
เกิดข้อผิดพลาด
เกิดข้อผิดพลาด: Exception: The number of columns in the data does not match the number of columns in the range. The data has 6 but the range has 5.

- เมื่อไปดูใน sheet พบการสร้าง sheet รายวิชาใหม่ แต่ไม่มีคะแนน มีแต่ header และไม่มีการสร้าง row รายวิชาใหม่นี้ใน sheet course_list ครับ